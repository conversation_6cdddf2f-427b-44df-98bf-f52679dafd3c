<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'User Dashboard - Indah Berkah Abadi')</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <link rel="stylesheet" href="{{ asset('css/user-dashboard-clean.css') }}">
    <link rel="stylesheet" href="{{ asset('css/mobile-fixes.css') }}">
    <link rel="stylesheet" href="{{ asset('css/user-dashboard-mobile-fixes.css') }}?v={{ time() }}">
    <link rel="stylesheet" href="{{ asset('css/user-dashboard-returns.css') }}?v={{ time() }}">
    <link rel="stylesheet" href="{{ asset('css/analytics-sidebar.css') }}?v={{ time() }}">

    @stack('styles')
</head>
<body class="font-inter antialiased bg-gray-50">
    <div class="dashboard-layout">
        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" onclick="closeSidebar()"></div>

        <!-- Sidebar -->
        <aside class="sidebar sidebar-user" id="sidebar">
            <div class="sidebar-header">
                <div class="flex items-center space-x-2">
                    <div class="h-8 w-8 bg-green-600 rounded flex items-center justify-center">
                        <span class="text-white font-bold text-sm">IBA</span>
                    </div>
                    <div>
                        <h2 class="font-semibold text-sm text-white">{{ auth()->user()->store ? auth()->user()->store->name : 'Toko' }}</h2>
                        <p class="text-xs text-green-200">{{ auth()->user()->store ? auth()->user()->store->location : 'Lokasi' }}</p>
                    </div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul class="space-y-1">
                    <li>
                        <a href="{{ route('user.dashboard') }}" class="nav-link {{ request()->routeIs('user.dashboard') ? 'active' : '' }}">
                            <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                            </svg>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('user.inventory') }}" class="nav-link {{ request()->routeIs('user.inventory') ? 'active' : '' }}">
                            <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            Stok Toko
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('user.deliveries') }}" class="nav-link {{ request()->routeIs('user.deliveries*') ? 'active' : '' }}">
                            <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Terima Barang
                        </a>
                    </li>

                    <li>
                        <a href="{{ route('user.returns.index') }}" class="nav-link {{ request()->routeIs('user.returns.index') ? 'active' : '' }}">
                            <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                            </svg>
                            Retur Produk
                        </a>
                    </li>

                    <li>
                        <a href="{{ route('user.returns.history') }}" class="nav-link {{ request()->routeIs('user.returns.history') ? 'active' : '' }}">
                            <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Riwayat Retur
                        </a>
                    </li>
                </ul>

                <!-- Settings Section -->
                <div class="px-2 py-4 border-t border-green-700">
                    <p class="text-xs font-medium text-green-200 uppercase tracking-wider mb-2">Pengaturan</p>
                    <ul class="space-y-1">
                        <li>
                            <a href="{{ route('user.profile') }}" class="nav-link {{ request()->routeIs('user.profile*') ? 'active' : '' }}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Profil Saya
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('user.settings') }}" class="nav-link {{ request()->routeIs('user.settings') ? 'active' : '' }}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Pengaturan
                            </a>
                        </li>
                        @if(auth()->user()->isAdmin())
                        <li>
                            <a href="{{ route('admin.dashboard') }}" class="nav-link hover:bg-blue-600 hover:text-white">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Dashboard Admin
                            </a>
                        </li>
                        @endif
                        <li>
                            <form method="POST" action="{{ route('logout') }}" class="w-full">
                                @csrf
                                <button type="submit" class="nav-link w-full text-left hover:bg-red-600 hover:text-white">
                                    <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    Keluar
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </nav>

            <div class="sidebar-footer">
                <div class="user-dashboard-user-menu">
                    <button class="user-dashboard-user-menu-button" id="userMenuButton" type="button" aria-expanded="false" aria-haspopup="true">
                        <div class="user-dashboard-user-info">
                            <div class="user-dashboard-user-avatar">
                                <span class="user-dashboard-user-initial">{{ substr(auth()->user()->name, 0, 1) }}</span>
                            </div>
                            <div class="user-dashboard-user-details">
                                <p class="user-dashboard-user-name">{{ auth()->user()->name }}</p>
                                <p class="user-dashboard-user-role">Store User</p>
                            </div>
                        </div>
                        <svg class="user-dashboard-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="user-dashboard-user-menu-dropdown" id="userMenuDropdown" role="menu" aria-labelledby="user-menu-button">
                        <a href="{{ route('user.profile') }}" class="user-dashboard-dropdown-item" role="menuitem">
                            <svg class="user-dashboard-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Lihat Profil
                        </a>
                        <a href="{{ route('user.profile.edit') }}" class="user-dashboard-dropdown-item" role="menuitem">
                            <svg class="user-dashboard-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Profil
                        </a>
                        <a href="{{ route('user.profile.password') }}" class="user-dashboard-dropdown-item" role="menuitem">
                            <svg class="user-dashboard-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2v6a2 2 0 01-2 2m-2-2H9m12 0V9a2 2 0 00-2-2M3 9a2 2 0 012-2h1m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            Ubah Kata Sandi
                        </a>
                        <div class="user-dashboard-dropdown-separator" role="separator"></div>
                        <form method="POST" action="{{ route('logout') }}" class="user-dashboard-logout-form">
                            @csrf
                            <button type="submit" class="user-dashboard-dropdown-item user-dashboard-logout-button" role="menuitem">
                                <svg class="user-dashboard-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Keluar
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="main-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="sidebar-toggle lg:hidden" onclick="toggleSidebar()" aria-label="Toggle sidebar">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                        <div class="hidden sm:block">
                            <h1 class="text-lg font-semibold text-gray-900">{{ auth()->user()->store ? auth()->user()->store->name : 'Dashboard Toko' }}</h1>
                        </div>
                    </div>
                    <!-- Header actions removed - notifications functionality removed as requested -->
                </div>
            </header>

            <div class="main-body">
                @yield('content')
            </div>
        </main>
    </div>

    <!-- Dashboard JavaScript -->
    @vite(['resources/js/app.js'])

    <!-- Timezone Script -->
    <script>
        // Store user timezone in session storage for JavaScript access
        @if(session('user_timezone'))
            sessionStorage.setItem('user_timezone', '{{ session('user_timezone') }}');
        @endif
    </script>

    <!-- Sidebar and Dropdown Scripts -->
    <script>
        // Sidebar functionality
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (sidebar && overlay) {
                if (sidebar.classList.contains('show')) {
                    closeSidebar();
                } else {
                    openSidebar();
                }
            }
        }

        function openSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (sidebar && overlay) {
                sidebar.classList.add('show');
                overlay.classList.add('show');

                // Prevent background scrolling on mobile
                if (window.innerWidth <= 768) {
                    document.body.style.overflow = 'hidden';
                    document.body.style.position = 'fixed';
                    document.body.style.width = '100%';
                }

                // Focus management for accessibility
                const firstFocusableElement = sidebar.querySelector('a, button, [tabindex]:not([tabindex="-1"])');
                if (firstFocusableElement) {
                    setTimeout(() => firstFocusableElement.focus(), 100);
                }
            }
        }

        function closeSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (sidebar && overlay) {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');

                // Restore scrolling
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 User dashboard scripts loading...');
            console.log('🔍 Checking sidebar elements...');

            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            const toggleButton = document.querySelector('.sidebar-toggle');

            console.log('Sidebar:', sidebar);
            console.log('Overlay:', overlay);
            console.log('Toggle button:', toggleButton);

            // Enhanced sidebar functionality for mobile
            if (toggleButton) {
                toggleButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleSidebar();
                });
            }

            // Sidebar overlay click handler
            if (overlay) {
                overlay.addEventListener('click', function(e) {
                    e.preventDefault();
                    closeSidebar();
                });
            }

            // Close sidebar on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && window.innerWidth <= 768) {
                    closeSidebar();
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    closeSidebar();
                }
            });

            // Touch/swipe support for mobile
            let touchStartX = 0;
            let touchEndX = 0;

            document.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            });

            document.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });

            function handleSwipe() {
                const swipeThreshold = 50;
                const swipeDistance = touchEndX - touchStartX;

                if (window.innerWidth <= 768) {
                    // Swipe right from left edge to open sidebar
                    if (touchStartX < 50 && swipeDistance > swipeThreshold) {
                        openSidebar();
                    }
                    // Swipe left to close sidebar
                    else if (sidebar && sidebar.classList.contains('show') && swipeDistance < -swipeThreshold) {
                        closeSidebar();
                    }
                }
            }

            // Disable the built-in dropdown functionality to prevent conflicts
            // The dedicated sidebar script will handle all dropdown functionality
            console.log('🔧 Built-in dropdown functionality disabled - using dedicated sidebar script');

            // Test navigation links
            const navLinks = document.querySelectorAll('.nav-link');
            console.log('📋 Found navigation links:', navLinks.length);

            navLinks.forEach((link, index) => {
                console.log(`Link ${index}:`, link.href, link.textContent.trim());

                link.addEventListener('click', function(e) {
                    console.log('🖱️ Navigation link clicked:', this.href);
                });
            });

            console.log('🎉 User dashboard scripts loaded successfully');
        });

        // Notification functionality removed as requested
    </script>

    <!-- Analytics Sidebar JavaScript -->
    <script src="{{ asset('js/analytics-sidebar.js') }}?v={{ time() }}"></script>

    <!-- Mobile Z-Index Fixes JavaScript -->
    <script src="{{ asset('js/user-dashboard-mobile-fixes.js') }}?v={{ time() }}"></script>

    <!-- Dedicated Sidebar Dropdown Fix JavaScript -->
    <script src="{{ asset('js/user-dashboard-sidebar-fix.js') }}?v={{ time() }}"></script>

    @stack('scripts')
</body>
</html>
