<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\Store;
use App\Models\Supplier;
use App\Models\StoreStock;
use App\Models\WarehouseStock;
use App\Models\StockMovement;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminReturnController extends Controller
{
    /**
     * Display a listing of warehouse-to-supplier returns only.
     */
    public function index(Request $request)
    {
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);

        // Only show active warehouse-to-supplier returns (store_id is null, supplier_id is not null)
        $query->whereNull('store_id')->whereNotNull('supplier_id');
        $query->whereIn('status', ['requested', 'approved']); // Only show active returns

        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);

        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

        // Apply date filter
        $query->whereBetween('return_date', [$startDate, $endDate]);
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }
        
        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // Sort by
        $sortBy = $request->get('sort_by', 'return_date');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if (in_array($sortBy, ['return_date', 'approved_date', 'completed_date', 'quantity', 'status'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('return_date', 'desc');
        }
        
        $returns = $query->paginate(15);
        
        // Get statistics (matching view expectations)
        $stats = [
            'total' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])->count(),
            'pending' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'requested')->count(),
            'approved' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'approved')->count(),
            'completed' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'completed')->count(),
        ];
        
        return view('admin.returns.index', compact('returns', 'stats', 'filterMonth'));
    }

    /**
     * Display return history (processed warehouse returns).
     */
    public function history(Request $request)
    {
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);

        // Only show processed warehouse-to-supplier returns
        $query->whereNull('store_id')->whereNotNull('supplier_id');
        $query->whereIn('status', ['completed', 'rejected']);

        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);

        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

        // Apply date filter based on when the return was actually processed
        $query->where(function($q) use ($startDate, $endDate) {
            $q->where(function($subQ) use ($startDate, $endDate) {
                // For completed returns, filter by completed_date
                $subQ->where('status', 'completed')
                     ->whereNotNull('completed_date')
                     ->whereBetween('completed_date', [$startDate, $endDate]);
            })->orWhere(function($subQ) use ($startDate, $endDate) {
                // For rejected returns, filter by approved_date (when rejection happened)
                $subQ->where('status', 'rejected')
                     ->whereNotNull('approved_date')
                     ->whereBetween('approved_date', [$startDate, $endDate]);
            });
        });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Sort by newest first
        $query->orderBy('return_date', 'desc');

        $returns = $query->paginate(15);

        // Calculate statistics (matching view expectations)
        $stats = [
            'total_processed' => ReturnModel::whereNull('store_id')->whereNotNull('supplier_id')
                ->whereIn('status', ['completed', 'rejected'])->count(),
            'completed' => ReturnModel::whereNull('store_id')->whereNotNull('supplier_id')
                ->where('status', 'completed')->count(),
            'rejected' => ReturnModel::whereNull('store_id')->whereNotNull('supplier_id')
                ->where('status', 'rejected')->count(),
            'in_transit' => 0, // History page shouldn't have in_transit, but view expects this key
        ];

        return view('admin.returns.history', compact('returns', 'stats', 'filterMonth'));
    }

    /**
     * Show the form for creating a new return.
     */
    public function create()
    {
        $products = Product::orderBy('name')->get();
        $stores = Store::orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();
        
        return view('admin.returns.create', compact('products', 'stores', 'suppliers'));
    }
    
    /**
     * Store a newly created return in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'product_id' => 'required|exists:products,id',
            'supplier_id' => 'required|exists:suppliers,id', // Required for warehouse returns
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|in:damaged,expired,defective,overstock,shortage,other',
            'description' => 'required|string|max:1000',
            'admin_notes' => 'nullable|string|max:1000',
            'return_date' => 'required|date',
        ], [
            'product_id.required' => 'Produk wajib dipilih',
            'product_id.exists' => 'Produk tidak valid',
            'supplier_id.required' => 'Supplier wajib dipilih untuk retur gudang',
            'supplier_id.exists' => 'Supplier tidak valid',
            'quantity.required' => 'Jumlah wajib diisi',
            'quantity.integer' => 'Jumlah harus berupa angka bulat',
            'quantity.min' => 'Jumlah minimal 1',
            'reason.required' => 'Alasan wajib dipilih',
            'reason.in' => 'Alasan tidak valid',
            'description.required' => 'Deskripsi detail wajib diisi',
            'description.max' => 'Deskripsi maksimal 1000 karakter',
            'admin_notes.max' => 'Catatan tambahan maksimal 1000 karakter',
            'return_date.required' => 'Tanggal retur wajib diisi',
            'return_date.date' => 'Format tanggal tidak valid',
        ]);

        $validatedData['requested_by'] = auth()->id();
        $validatedData['status'] = 'requested';
        $validatedData['store_id'] = null; // Ensure this is a warehouse return

        $return = ReturnModel::create($validatedData);
        
        return redirect()->route('admin.returns.index')
            ->with('success', 'Permintaan retur berhasil dibuat');
    }
    
    /**
     * Display the specified return.
     */
    public function show(ReturnModel $return)
    {
        $return->load(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
        
        return view('admin.returns.show', compact('return'));
    }
    
    /**
     * Approve a return request.
     */
    public function approve(Request $request, ReturnModel $return)
    {
        if ($return->status !== 'requested') {
            return redirect()->route('admin.returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        $return->update([
            'status' => 'approved',
            'approved_date' => now(),
            'approved_by' => auth()->id(),
            'admin_notes' => $validatedData['admin_notes'],
        ]);
        
        return redirect()->route('admin.returns.index')
            ->with('success', 'Permintaan retur berhasil disetujui');
    }
    
    /**
     * Reject a return request.
     */
    public function reject(Request $request, ReturnModel $return)
    {
        if ($return->status !== 'requested') {
            return redirect()->route('admin.returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ], [
            'admin_notes.required' => 'Alasan penolakan wajib diisi',
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        $return->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'admin_notes' => $validatedData['admin_notes'],
        ]);
        
        return redirect()->route('admin.returns.index')
            ->with('success', 'Permintaan retur berhasil ditolak');
    }
    
    /**
     * Complete a return.
     */
    public function complete(Request $request, ReturnModel $return)
    {
        if (!in_array($return->status, ['approved', 'in_transit'])) {
            return redirect()->route('admin.returns.index')
                ->with('error', 'Retur ini tidak dapat diselesaikan');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        DB::transaction(function () use ($return, $validatedData) {
            // Update return status
            $return->update([
                'status' => 'completed',
                'completed_date' => now(),
                'admin_notes' => ($return->admin_notes ?? '') . "\n\nCompleted: " . ($validatedData['admin_notes'] ?? 'Retur selesai'),
            ]);
            
            // Handle stock adjustments based on return source
            if ($return->isFromStore()) {
                // Return from store - reduce store stock
                $storeStock = StoreStock::where('store_id', $return->store_id)
                    ->where('product_id', $return->product_id)
                    ->first();
                
                if ($storeStock && $storeStock->quantity >= $return->quantity) {
                    $storeStock->decrement('quantity', $return->quantity);
                    
                    // Record stock movement
                    StockMovement::create([
                        'product_id' => $return->product_id,
                        'type' => 'out',
                        'source' => 'store_adjustment',
                        'quantity' => -$return->quantity,
                        'previous_stock' => $storeStock->quantity + $return->quantity,
                        'new_stock' => $storeStock->quantity,
                        'reference_type' => 'ReturnModel',
                        'reference_id' => $return->id,
                        'notes' => 'Retur dari toko: ' . $return->store->name,
                        'created_by' => auth()->id(),
                    ]);
                }
            }
            
            // If return goes to supplier, reduce warehouse stock
            if ($return->isToSupplier()) {
                // This would be handled when supplier accepts the return
                // For now, we just mark it as completed
            }
        });
        
        return redirect()->route('admin.returns.index')
            ->with('success', 'Retur berhasil diselesaikan');
    }

    /**
     * Forward a store return to supplier.
     */
    public function forwardToSupplier(Request $request, ReturnModel $return)
    {
        // Only allow forwarding store returns that are approved
        if (!$return->isFromStore() || $return->status !== 'approved') {
            return redirect()->route('admin.returns.index')
                ->with('error', 'Retur ini tidak dapat diteruskan ke supplier');
        }

        $validatedData = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'supplier_id.required' => 'Supplier wajib dipilih',
            'supplier_id.exists' => 'Supplier tidak valid',
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        DB::transaction(function () use ($return, $validatedData) {
            // Update the existing return to forward to supplier
            $return->update([
                'supplier_id' => $validatedData['supplier_id'],
                'status' => 'in_transit',
                'admin_notes' => ($return->admin_notes ?? '') . "\n\nForwarded to supplier: " . ($validatedData['admin_notes'] ?? 'Diteruskan ke supplier'),
            ]);

            // Reduce store stock since it's being returned
            $storeStock = StoreStock::where('store_id', $return->store_id)
                ->where('product_id', $return->product_id)
                ->first();

            if ($storeStock && $storeStock->quantity >= $return->quantity) {
                $storeStock->decrement('quantity', $return->quantity);

                // Record stock movement
                StockMovement::create([
                    'product_id' => $return->product_id,
                    'type' => 'out',
                    'source' => 'return_to_supplier',
                    'quantity' => -$return->quantity,
                    'previous_stock' => $storeStock->quantity + $return->quantity,
                    'new_stock' => $storeStock->quantity,
                    'reference_type' => 'ReturnModel',
                    'reference_id' => $return->id,
                    'notes' => 'Retur diteruskan ke supplier: ' . $return->supplier->name,
                    'created_by' => auth()->id(),
                ]);
            }
        });

        return redirect()->route('admin.returns.index')
            ->with('success', 'Retur berhasil diteruskan ke supplier');
    }
}
